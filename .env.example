# SmartHR Docker Environment Configuration
# Copy this file to .env and fill in your actual values

# Backend Environment Variables
AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=your-embeddings-deployment
AZURE_OPENAI_ENDPOINT=https://your-openai-endpoint.openai.azure.com/
AZURE_OPENAI_API_KEY=your-azure-openai-api-key
OPENAI_API_VERSION=2023-05-15
LANGCHAIN_TRACING_V2=false
LANGCHAIN_API_KEY=your-langchain-api-key
GROQ_API_KEY=your-groq-api-key

# Database Configuration
POSTGRES_USER=smarthr_user
POSTGRES_PASSWORD=your-secure-password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=smarthr_db

# Application Insights
APPLICATIONINSIGHTS_CONNECTION_STRING=your-app-insights-connection-string

# Position Match Models
POSITION_MATCH_MODELS_ORDER=model1,model2,model3

# Frontend Environment Variables
VITE_API_URL=http://localhost:8080
NODE_ENV=development
