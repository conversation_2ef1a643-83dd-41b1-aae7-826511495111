version: '3.8'

services:
  # Backend service
  smarthr-backend:
    build:
      context: ./smarthr-be
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=${AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - OPENAI_API_VERSION=${OPENAI_API_VERSION}
      - LANGCHAIN_TRACING_V2=${LANGCHAIN_TRACING_V2}
      - LANGCHAIN_API_KEY=${LANGCHAIN_API_KEY}
      - GROQ_API_KEY=${GROQ_API_KEY}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DB=${POSTGRES_DB}
      - APPLICATIONINSIGHTS_CONNECTION_STRING=${APPLICATIONINSIGHTS_CONNECTION_STRING}
      - POSITION_MATCH_MODELS_ORDER=${POSITION_MATCH_MODELS_ORDER}
    volumes:
      - ./smarthr-be:/app
      - /app/__pycache__
    networks:
      - smarthr-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend service
  smarthr-frontend:
    build:
      context: ./smarthr-fe
      dockerfile: Dockerfile
      target: development
    ports:
      - "5173:5173"
    environment:
      - VITE_BASE_API_URL=http://localhost:8080
      - NODE_ENV=development
    volumes:
      - ./smarthr-fe:/app
      - /app/node_modules
    networks:
      - smarthr-network
    depends_on:
      smarthr-backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  smarthr-network:
    driver: bridge
