import os

import psycopg2
from dotenv import load_dotenv

load_dotenv()

user = os.getenv("POSTGRES_USER", "postgres")
password = os.getenv("POSTGRES_PASSWORD", "")
host = os.getenv("POSTGRES_HOST", "localhost")
port = os.getenv("POSTGRES_PORT", "5432")
database = os.getenv("POSTGRES_DB", "postgres")

connection = psycopg2.connect(
    user=user, password=password, host=host, port=port, database=database
)

cursor = connection.cursor()

# Enable pgcrypto for gen_random_uuid()
# cursor.execute("CREATE EXTENSION IF NOT EXISTS pgcrypto;")

# Create pgvector extension if not exists
cursor.execute("CREATE EXTENSION IF NOT EXISTS vector;")

# Create fuzzymatch extension if not exists
cursor.execute("CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;")


# Projects table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_name TEXT,
    name TEXT,
    description TEXT,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
"""
)

# Candidates table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS candidates_smarthr (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proj_id UUID REFERENCES projects(id),
    candidate_info JSONB,
    suggested_positions JSONB,
    analysis_status TEXT,
    to_be_embebbed TEXT,
    embedding VECTOR(1536),
    sparse_embedding JSONB,
    Reason_Info JSONB,
    is_active BOOLEAN DEFAULT true,
    is_deleted BOOLEAN DEFAULT false,
    last_matching TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT NOW(),
    updated_by TEXT
);
"""
)

# add columns is_deleted, created_by, updated_by to candidates_smarthr table if not exists
cursor.execute(
    """
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'candidates_smarthr' 
            AND column_name = 'is_deleted'
        ) THEN
            ALTER TABLE candidates_smarthr ADD COLUMN is_deleted BOOLEAN DEFAULT false;
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'candidates_smarthr' 
            AND column_name = 'created_by'
        ) THEN
            ALTER TABLE candidates_smarthr ADD COLUMN created_by TEXT;
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'candidates_smarthr' 
            AND column_name = 'updated_by'
        ) THEN
            ALTER TABLE candidates_smarthr ADD COLUMN updated_by TEXT;
        END IF;
        
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'candidates_smarthr' 
            AND column_name = 'sparse_embedding'
        ) THEN
            ALTER TABLE candidates_smarthr ADD COLUMN sparse_embedding JSONB;
        END IF;
    """
)


# Positions table with UUID primary key
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS positions_smarthr (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    proj_id UUID REFERENCES projects(id),
    position_info JSONB,
    top_candidates JSONB,
    to_be_embebbed TEXT,
    embedding VECTOR(1536),
    sparse_embedding JSONB,
    last_matching TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    priority_skills JSONB,
    external_id TEXT
);
"""
)

# add column external_id to positions_smarthr table if not exists
cursor.execute(
    """
    IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'positions_smarthr' 
            AND column_name = 'priority_skills'
        ) THEN
            ALTER TABLE positions_smarthr ADD COLUMN priority_skills JSONB;
    END IF;
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'positions_smarthr' 
        AND column_name = 'external_id'
    ) THEN
        ALTER TABLE positions_smarthr ADD COLUMN external_id TEXT;
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'positions_smarthr' 
        AND column_name = 'sparse_embedding'
    ) THEN
        ALTER TABLE positions_smarthr ADD COLUMN sparse_embedding JSONB;
    END IF;
""")


# Create indexes
cursor.execute(
    """
CREATE INDEX IF NOT EXISTS candidates_embedding_idx 
ON candidates_smarthr USING hnsw (embedding vector_cosine_ops);
"""
)

# cursor.execute(
#     """
# CREATE INDEX IF NOT EXISTS positions_embedding_idx 
# ON positions_smarthr USING hnsw (embedding vector_cosine_ops);
# """
# )

# cursor.execute(
#     """
# CREATE INDEX idx_candidates_personal_info_email 
# ON candidates_smarthr USING GIN ((candidate_info->'personal_info'->>'email'));

# """
# )

# cursor.execute(
#     """
# CREATE INDEX idx_candidates_personal_info_full_name 
# ON candidates_smarthr USING GIN ((candidate_info->'personal_info'->>'full_name'));
# """
# )


# -------------------- ENUM interview_status ------------------------
cursor.execute(
    """
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'interview_status') THEN
        CREATE TYPE interview_status AS ENUM
            ('scheduled', 'in_progress', 'completed', 'cancelled', 'not_scheduled');
    ELSE
        -- Add missing enum values if they don't exist
        IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'not_scheduled' AND enumtypid = 'interview_status'::regtype) THEN
            ALTER TYPE interview_status ADD VALUE IF NOT EXISTS 'not_scheduled';
        END IF;
    END IF;
END $$;
"""
)

# ------------------ TABLE interview_questions ----------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS interview_questions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    position_id UUID NOT NULL
               REFERENCES positions_smarthr(id) ON DELETE CASCADE,
    data JSONB,
    allow_regeneration BOOLEAN DEFAULT TRUE, -- new column to saved question flag and do not allow regeneration
    created_at TIMESTAMP DEFAULT NOW(),
    created_by TEXT,
    updated_at TIMESTAMP DEFAULT NOW(),
    updated_by TEXT,
    UNIQUE (position_id)
);
IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'interview_questions'
    AND column_name = 'allow_regeneration'
) THEN
    ALTER TABLE interview_questions ADD COLUMN allow_regeneration BOOLEAN DEFAULT TRUE;
END IF;
IF NOT EXISTS (
    SELECT 1
    FROM information_schema.columns
    WHERE table_name = 'interview_questions'
    AND column_name = 'created_by'
) THEN
    ALTER TABLE interview_questions ADD COLUMN created_by TEXT;
    ALTER TABLE interview_questions ADD COLUMN updated_by TEXT;
END IF;
"""
)

# ----------------------- TABLE interviews --------------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS interviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

    position_id  UUID NOT NULL
                 REFERENCES positions_smarthr(id) ON DELETE CASCADE,
    candidate_id UUID NOT NULL
                 REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
    analysis_data JSONB,
    feedback_hr        JSONB,
    interview_date_hr  TIMESTAMP,
    feedback_date_hr   TIMESTAMP,
    recruiter_hr_id    TEXT,
    scheduled_hr_id    TEXT,
    status_hr          interview_status,
    recommendation_hr  BOOLEAN,
    transcript_hr      TEXT,

    feedback_tec       JSONB,
    recruiter_tec_id   TEXT,
    scheduled_tec_id   TEXT,
    interview_date_tec TIMESTAMP,
    feedback_date_tec  TIMESTAMP,
    status_tec         interview_status,
    recommendation_tec BOOLEAN,
    transcript_tec     TEXT,

    anwers_data   JSONB,
    interview_data JSONB,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
"""
)

# add column is_deleted to interviews table
cursor.execute(
    """
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'interviews' 
            AND column_name = 'recruiter_hr_id'
        ) THEN
            ALTER TABLE interviews ADD COLUMN recruiter_hr_id TEXT;
            ALTER TABLE interviews ADD COLUMN scheduled_hr_id TEXT;
            ALTER TABLE interviews ADD COLUMN recruiter_tec_id TEXT;
            ALTER TABLE interviews ADD COLUMN scheduled_tec_id TEXT;
        END IF;
    """
)

# add column analysis_data to interviews table
cursor.execute(
    """
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'interviews' 
            AND column_name = 'analysis_data'
        ) THEN
            ALTER TABLE interviews ADD COLUMN analysis_data JSONB;
        END IF;
    """
)

# ----------------------- TABLE candidate_notes ------------------------
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS candidate_notes (
    id              UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    candidate_id    UUID NOT NULL REFERENCES candidates_smarthr(id) ON DELETE CASCADE,
    notes           JSONB,
    created_by      TEXT,
    created_at      TIMESTAMP DEFAULT NOW(),
    updated_by      TEXT,
    updated_at      TIMESTAMP DEFAULT NOW()
);
"""
)

# ----------------------- TRIGGER updated_at ------------------------
cursor.execute(
    """
CREATE OR REPLACE FUNCTION trg_set_updated_at()
RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;
"""
)

cursor.execute("DROP TRIGGER IF EXISTS set_updated_at_interviews ON interviews;")
cursor.execute(
    """
CREATE TRIGGER set_updated_at_interviews
BEFORE UPDATE ON interviews
FOR EACH ROW EXECUTE FUNCTION trg_set_updated_at();
"""
)

# --------------------------- INDEXES -------------------------------
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_position   ON interviews(position_id);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_candidate  ON interviews(candidate_id);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_status_hr  ON interviews(status_hr);"
)
cursor.execute(
    "CREATE INDEX IF NOT EXISTS idx_interviews_status_tec ON interviews(status_tec);"
)
connection.commit()
cursor.close()
connection.close()
