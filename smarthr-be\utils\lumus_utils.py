import os
import tempfile
import httpx
import psycopg2
import logging
from fastapi import UploadFile, HTTPException
from models.candidate import UploadFileResponse

logger = logging.getLogger(__name__)
LUMUS_URL = os.getenv("LUMUS_API_URL", "localhost")


# Process a single file using the Lumus API.
# This function reads the file, converts it to PDF if necessary, and sends it to the Lumus API for processing.
async def process_file_lumus(file: UploadFile) -> UploadFileResponse:
    """
    Process a single file using the Lumus API.

    Args:
        file (UploadFile): The file to process.

    Returns:
        UploadFileResponse: The response after processing the file.
    """
    if not file:
        return UploadFileResponse(
            candidate_info={},
            error=True,
            error_message="No file provided"
        )
    timeout_seconds = float(os.getenv("LUMUS_API_TIMEOUT", "320.0"))
    timeout = httpx.Timeout(timeout_seconds)
    form_data = await get_form_data_lumus(file)
    if not form_data:
        return UploadFileResponse(
            candidate_info={},
            error=True,
            error_message="No file content provided"
        )
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            result = await client.post(LUMUS_URL + "/process", files=form_data)
            if result.status_code == 200:
                candInfo = result.json().get("response")
                if candInfo is None or candInfo.get("personal_info") is None:
                    return UploadFileResponse(
                        candidate_info=candInfo,
                        error=True,
                        error_message="personal_info not found in file"
                    )
                if candInfo.get("personal_info").get("email") is None:
                    return UploadFileResponse(
                        candidate_info=candInfo,
                        error=True,
                        error_message="email not found in file"
                    )
                if candInfo.get("work_experience") is None:
                    return UploadFileResponse(
                        candidate_info=candInfo,
                        error=True,
                        error_message="work_experience not found in file"
                    )
                return UploadFileResponse(
                    candidate_info=candInfo,
                    error=False,
                    error_message=""
                )
            else:
                return UploadFileResponse(
                    candidate_info={},
                    error=True,
                    error_message=str(result.status_code) + " " + result.text
                )
        except Exception as e:
            return UploadFileResponse(
                candidate_info={},
                error=True,
                error_message="Error calling Lumus. " + str(e)
            )


# Prepare form data for the Lumus API.
# This function reads the file content, converts DOCX files to PDF if necessary, and prepares the form data for the API request.
async def get_form_data_lumus(file: UploadFile):
    """
    Convert file to PDF if needed and prepare form data for Lumus API.

    Args:
        file (UploadFile): The file to process.

    Returns:
        dict: Form data for the API.
    """
    from utils.file_utils import convert_docx_to_pdf  # Import here to avoid circular import
    try:
        file_content = await file.read()
        ext = file.filename.split('.')[-1].lower()

        with tempfile.TemporaryDirectory() as tmpdir:
            original_path = os.path.join(tmpdir, file.filename)
            with open(original_path, "wb") as f:
                f.write(file_content)

            if file.content_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                pdf_path = os.path.join(tmpdir, file.filename.replace(".docx", ".pdf"))
                pdf_path = convert_docx_to_pdf(original_path, pdf_path)
                with open(pdf_path, "rb") as pdf_file:
                    converted_content = pdf_file.read()
                form_data = {
                    "file": ("file.pdf", converted_content, "application/pdf"),
                    "action": (None, "cv")
                }
            else:
                form_data = {
                    "file": (f"file.{ext}", file_content, file.content_type),
                    "action": (None, "cv")
                }
        return form_data
    except psycopg2.Error as e:
        logger.error(f"Database error in get_form_data: {str(e)}")
        return HTTPException(status_code=500, detail=f"Database Error in get_form_data: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException in get_form_data: {str(e.detail)}")
        return e
    except Exception as e:
        logger.error(f"Unexpected error in get_form_data: {str(e)}")
        return HTTPException(status_code=500, detail=f"Unexpected Error in get_form_data: {str(e)}")


# Extract data from multiple files using the Lumus API.
# This function processes each file concurrently and returns a list of responses.
async def extract_data_lumus(files):
    """
    Process multiple files concurrently using the Lumus API.

    Args:
        files (List[UploadFile]): List of files to process.

    Returns:
        List[UploadFileResponse]: List of processed file responses.
    """
    tasks = [process_file_lumus(file) for file in files]
    import asyncio
    return await asyncio.gather(*tasks)
