{"name": "smarthr-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage"}, "dependencies": {"@azure/msal-browser": "^3.27.0", "@azure/msal-react": "^2.2.0", "@tanstack/react-query": "^5.59.16", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@vitest/ui": "^3.2.4", "antd": "^5.21.5", "axios": "^1.7.7", "dotenv": "^16.4.7", "jsdom": "^26.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.27.0", "vitest": "^3.2.4"}, "devDependencies": {"@eslint/js": "^9.11.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.2", "dotenv": "^16.4.7", "eslint": "^9.11.1", "eslint-plugin-react": "^7.37.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "standard": "^16.0.4", "vite": "^5.4.8"}, "eslintConfig": {"extends": "./node_modules/standard/eslintrc.json"}}