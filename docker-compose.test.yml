version: '3.8'

services:
  # Backend test service
  smarthr-backend-test:
    build:
      context: ./smarthr-be
      dockerfile: Dockerfile
    environment:
      - AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS=${AZURE_OPENAI_DEPLOYMENT_NAME_EMBEDDINGS:-test-embeddings}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT:-https://test.openai.azure.com/}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY:-test-key}
      - OPENAI_API_VERSION=${OPENAI_API_VERSION:-2023-05-15}
      - LANGCHAIN_TRACING_V2=false
      - LANGCHAIN_API_KEY=
      - GROQ_API_KEY=${GROQ_API_KEY:-test-key}
      - POSTGRES_USER=${POSTGRES_USER:-test_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-test_password}
      - POSTGRES_HOST=${POSTGRES_HOST:-localhost}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_DB=${POSTGRES_DB:-test_db}
      - APPLICATIONINSIGHTS_CONNECTION_STRING=${APPLICATIONINSIGHTS_CONNECTION_STRING:-InstrumentationKey=00000000-0000-0000-0000-000000000000}
      - POSITION_MATCH_MODELS_ORDER=${POSITION_MATCH_MODELS_ORDER:-["gpt-4o", "gpt-4o-mini"]}
    volumes:
      - ./smarthr-be:/app
      - /app/__pycache__
    networks:
      - smarthr-test-network
    command: ["python", "-m", "pytest", "tests/", "-v"]

  # Frontend test service
  smarthr-frontend-test:
    build:
      context: ./smarthr-fe
      dockerfile: Dockerfile
      target: test
    environment:
      - NODE_ENV=test
      - VITE_API_URL=http://smarthr-backend-test:8080
    volumes:
      - ./smarthr-fe:/app
      - /app/node_modules
    networks:
      - smarthr-test-network
    depends_on:
      - smarthr-backend-test

  # Test database service (optional)
  test-postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-test_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-test_password}
      - POSTGRES_DB=${POSTGRES_DB:-test_db}
    ports:
      - "5433:5432"
    networks:
      - smarthr-test-network
    volumes:
      - test_postgres_data:/var/lib/postgresql/data

networks:
  smarthr-test-network:
    driver: bridge

volumes:
  test_postgres_data:
